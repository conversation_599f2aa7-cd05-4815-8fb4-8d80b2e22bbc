// require('ts-node/register');
// module.exports = require('./metro.config.ts');
const {getDefaultConfig} = require('expo/metro-config');
const {wrapWithReanimatedMetroConfig} = require('react-native-reanimated/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);
// eslint-disable-next-line @typescript-eslint/no-unsafe-call -- valid path
config.resolver.sourceExts.push('cjs', 'mjs');
// config.resolver.assetExts.push('cjs');
// defaultConfig.resolver.unstable_enablePackageExports = true;
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: true,
  },
});

module.exports = wrapWithReanimatedMetroConfig(config);
