fly-fit/
├── 📱 src/                         # React Native Mobile App
│   ├── components/                 # Feature-specific UI components
│   │   ├── Auth/                   # Authentication flows
│   │   ├── Challenges/             # Challenge management
│   │   ├── HealthSync/             # Health data integration
│   │   ├── Admin/                  # Administrative features
│   │   └── Wellness/               # Content and streaks
│   ├── pages/                      # Screen components (navigation targets)
│   ├── contexts/                   # State management and data hooks
│   ├── hooks/                      # Reusable React hooks
│   ├── types/                      # TypeScript type definitions
│   ├── utils/                      # Utility functions and business logic
│   └── base-components/            # Atomic UI components
│
├── ⚡ functions/                   # Firebase Backend Services
│   ├── src/                        # Cloud Functions source code
│   │   ├── endpoints/              # HTTP API endpoints
│   │   ├── endpointHelpers/        # Business logic helpers
│   │   ├── types/                  # Backend type definitions
│   │   └── utils/                  # Server-side utilities
│   ├── emails/                     # Email templates (React Email)
│   ├── report-generation/          # PDF generation service
│   └── swagger-docs/               # API documentation (unmaintained)
│
├── 🌐 web-sign-up/                  # Web Registration Application
│   └── src/                        # React web app source
│
├── 📊 data/                        # Static Content and Configuration
│   ├── config/                     # Environment-specific settings
│   └── wellness/                   # Static wellness content
│
├── 🛠️ scripts/                     # Automation and Utilities
│   ├── ci-cd*.zx.mjs               # Deployment automation
│   ├── import-*.zx.mjs             # Data import tools
│   └── *.appsScript.mjs            # Google Apps Script integrations
│
├── 📚 docs/                        # Documentation
├── 🎨 assets/                      # Static assets and media
└── 📋 Configuration Files          # Package.json, Firebase config, etc.

// Screen Level - Navigation target
const ViewChallengeScreen = () => (
  <ViewChallengeContainer challengeId={challengeId} />
);

// Feature Level - Business logic container
const ViewChallengeContainer = ({challengeId}) => {
  const challenge = useChallenge(challengeId);
  const user = useAppUser();

  return (
    <ViewChallengePresenter
      challenge={challenge}
      user={user}
    />
  );
};

// Feature Level - UI presentation
const ViewChallengePresenter = ({challenge, user}) => (
  <>
    <ChallengeHeader challenge={challenge} />
    <ChallengeProgress challenge={challenge} user={user} />
    <ChallengeLeaderboard challenge={challenge} />
  </>
);

// Component Level - Composite components
const ChallengeHeader = ({challenge}) => (
  <Box>
    <ChallengeImage source={challenge.imageUrl} />
    <ChallengeTitle text={challenge.title} />
    <ChallengeStatus status={challenge.status} />
  </Box>
);

// Base Level - Atomic components
const ChallengeTitle = ({text}) => (
  <Text variant="headlineMedium">{text}</Text>
);

// Business Logic Hook
export const useChallenge = (challengeId: string) => {
  // Data fetching
  const {data: challenge, isLoading} = useFirestoreQuery({
    q: () => query(db.challenges, where('id', '==', challengeId)),
    queryKey: ['challenge', challengeId],
    collectionReference: db.challenges,
  });

  // Derived state
  const isActive = useMemo(() =>
    challenge?.status === 'active' &&
    challenge?.endDate > new Date()
  , [challenge]);

  // Mutation for joining challenge
  const joinChallengeMutation = useMutation({
    mutationFn: async () => {
      if (!challenge) throw new Error('No challenge found');
      return await firebaseApi.joinChallenge(challenge.id);
    },
    onSuccess: () => {
      // Invalidate related queries to refresh data
      queryClient.invalidateQueries(['challenge', challengeId]);
      queryClient.invalidateQueries(['challengeParticipants', challengeId]);
    },
  });

  return {
    challenge,
    isLoading,
    isActive,
    joinChallenge: joinChallengeMutation.mutate,
    isJoining: joinChallengeMutation.isPending,
    joinError: joinChallengeMutation.error,
  };
};

// Firebase Query Hook
export const useChallengeParticipants = (challengeId: string) => {
  return useFirestoreQuery({
    q: () => query(
      db.challengeParticipants,
      where('challengeId', '==', challengeId),
      orderBy('joinedAt', 'desc')
    ),
    queryKey: ['challengeParticipants', challengeId],
    isEnabled: !!challengeId,
    collectionReference: db.challengeParticipants,
  });
};

// Global State Atom
const userPreferencesAtom = atom<UserPreferences>({
  theme: 'light',
  notifications: true,
  units: 'metric',
});

// Derived Atom
const isDarkModeAtom = atom(
  (get) => get(userPreferencesAtom).theme === 'dark'
);

// Write-only Atom
const toggleThemeAtom = atom(
  null,
  (get, set) => {
    const current = get(userPreferencesAtom);
    set(userPreferencesAtom, {
      ...current,
      theme: current.theme === 'light' ? 'dark' : 'light'
    });
  }
);

// Hook Wrappers (never export atoms directly)
export const useUserPreferences = () => useAtomValue(userPreferencesAtom);
export const useIsDarkMode = () => useAtomValue(isDarkModeAtom);
export const useToggleTheme = () => useSetAtom(toggleThemeAtom);

// Real endpoint example: functions/src/endpoints/requests/challenge/getChallengeEmails.ts
export const getChallengeEmails = onFirebaseRequest(
  composeMiddleware(
    'getChallengeEmails',
    async (request, response) => {
      // 1. Input Validation
      const {challengeId} = request.body as Record<string, UUIDString | undefined>;
      if (!isDefinedString(challengeId)) {
        LOGGER.warn('challengeId is required', request);
        response.status(StatusCodes.BAD_REQUEST_400).send('challengeId is required').end();
        return;
      }

      // 2. Data Fetching & Validation
      const challenge = await getChallengeById(challengeId);
      if (!challenge) {
        LOGGER.warn(`No challenge found for id ${challengeId}`);
        response
          .status(StatusCodes.BAD_REQUEST_400)
          .send(`No challenge found for id ${challengeId}`)
          .end();
        return;
      }

      // 3. Business Logic
      const userIds = challenge.participantIds;
      const appUsers = await getAppUsersByIds(userIds);
      const emails = [...appUsers.values()].map(appUser => appUser.email);

      // 4. Response Validation & Return
      if (isEmptyArray(emails)) {
        response.status(StatusCodes.NOT_FOUND_404).send('user emails not found').end();
        return;
      }

      response.status(StatusCodes.OK_200).send(emails.join(',')).end();
    },
    // 5. Middleware Stack (runs in order)
    withValidPostRequest,  // Validates POST request structure
    withApiKey('************************************'), // Validates API key
  ),
);