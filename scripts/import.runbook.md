---
runme:
  id: 01HH16ASHCGTNCJTVD7B6JG91J
  version: v3
---

# Import From Google Sheets

This document describes how to import data into the FlyFit Firestore database from Google Sheets as a source.

## Create Users from Google Sheets

The following section is a runbook documenting the steps to import users from a Google Sheet into Firebase.

### Export User Data From Sheets

- Structure of google sheets, 5 columns of

   - email
   - phoneNumber
   - firstName
   - lastName
   - organizationId

- [Source spreadsheet](https://docs.google.com/spreadsheets/d/1urfDYvUaoUn0nLtsX4e9Xy_zfmH59iQAi8ftma86khA/edit#gid=0)
- [Export Chadwick Apps Script](https://script.google.com/u/0/home/<USER>/1ye35q54S9UuZir_XVAaZhjA8IXyD7DUtDcNXKgU8WiwhM0q7aqlx8IAu/edit)
- Use this script within AppsScript to export the data to JSON.

   - `./scripts/export-user-data.appsScript.mjs`

- ⬇️ Copy that output from the logs from the AppScript and paste it/set it equal to this `jsonObject` variable below. ⬇️

```javascript {"id":"01HH196KDMSPB1H967Z93S5X8J"}
const jsonObject = 	{}

const jsonObjectWithObjects = {
  ...jsonObject,
  isCreateUserAccounts: false,
  isImportToChallenges: true,
  isSendWelcomeEmails: false
}

// Convert the JSON object to a string
const jsonString = JSON.stringify(jsonObjectWithObjects, null, 2);

// Write to a file
import {writeFile} from 'fs';
writeFile('users.source.json', jsonString, 'utf8', (err) => {
  if (err) {
    console.error(err);
    return;
  }
  console.log('Saved to users.source.json');
});

```

### Import Users Into Firestore via Backend API

Now we must simply upload this JSON to our back API endpoint `/importUsers` to import all of these users and see their success/failure status.

```bash {"id":"01HH196KDMSPB1H967ZGTJE6Y7"}
# DEV
# bun ./import-users.zx.mjs --env dev --file ./users.source.json
# PROD
bun ./import-users.zx.mjs --env prod --file ./users.source.json
```

## Import Mileage Data

The following file is used to run the import of the mileage data from the Google Sheets into Firebase Firestore.

`./scripts/mileage-data-import.appsScript.mjs`

## Backup Data

We must first back up our data just in case something goes wrong.

```bash {"id":"01HPGJ9TTQPW8AV9E4ZH554RVA"}
# DEV
# cd .. && bun backup:dev
# PROD
cd .. && bun backup:prod
```
