import {LOGGER, onFirebaseRequest} from '../../../firebase';
import {getAppUsersByIds, getChallengeById} from '../../../firestore';
import {StatusCodes, type UUIDString} from '../../../types';
import {
  composeMiddleware,
  isDefinedString,
  isEmptyArray,
  withApiKey,
  withValidPostRequest,
} from '../../../utils';

export const getChallengeEmails = onFirebaseRequest(
  composeMiddleware(
    'getChallengeEmails',
    async (request, response) => {
      const {challengeId} = request.body as Record<string, UUIDString | undefined>;
      if (!isDefinedString(challengeId)) {
        LOGGER.warn('challengeId is required', request);
        response.status(StatusCodes.BAD_REQUEST_400).send('challengeId is required').end();
        return;
      }

      const challenge = await getChallengeById(challengeId);
      if (!challenge) {
        LOGGER.warn(`No challenge found for id ${challengeId}`);
        response
          .status(StatusCodes.BAD_REQUEST_400)
          .send(`No challenge found for id ${challengeId}`)
          .end();
        return;
      }

      const userIds = challenge.participantIds;
      const appUsers = await getAppUsersByIds(userIds);
      const emails = [...appUsers.values()].map(appUser => appUser.email);

      if (isEmptyArray(emails)) {
        response.status(StatusCodes.NOT_FOUND_404).send('user emails not found').end();
        return;
      }

      response.status(StatusCodes.OK_200).send(emails.join(',')).end();
    },
    withValidPostRequest,
    withApiKey('************************************'),
  ),
);
